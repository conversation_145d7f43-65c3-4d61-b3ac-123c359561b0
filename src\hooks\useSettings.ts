import { useState, useEffect } from 'react'

interface SystemSettings {
  systemName: string
  timezone: string
  dateFormat: string
  language: string
  maintenanceMode: boolean
  debugMode: boolean
}

interface UserManagementSettings {
  defaultRole: string
  selfRegistration: boolean
  passwordRequirements: string
  sessionTimeout: number
  maxUsers: number
}

interface SecuritySettings {
  twoFactor: string
  loginAttempts: number
  lockoutDuration: number
  passwordExpiry: number
  ipWhitelist: boolean
}

interface NotificationSettings {
  newRegistrationAlerts: boolean
  dailySummary: boolean
  maintenanceAlerts: boolean
  emailNotifications: string
  slackWebhook: string
}

interface AllSettings {
  system: SystemSettings
  userManagement: UserManagementSettings
  security: SecuritySettings
  notifications: NotificationSettings
}

export function useSettings() {
  const [settings, setSettings] = useState<Partial<AllSettings>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/settings')
      
      if (!response.ok) {
        throw new Error('Failed to fetch settings')
      }

      const data = await response.json()
      
      if (data.success) {
        // Transform the API response to a more usable format
        const transformedSettings: Partial<AllSettings> = {}
        
        Object.keys(data.settings).forEach(category => {
          const categorySettings = data.settings[category]
          const settingsObject: any = {}
          
          categorySettings.forEach((setting: any) => {
            settingsObject[setting.key] = setting.value
          })
          
          transformedSettings[category as keyof AllSettings] = settingsObject
        })
        
        setSettings(transformedSettings)
        setError(null)
      } else {
        throw new Error('Invalid response format')
      }
    } catch (err) {
      console.error('Failed to fetch settings:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch settings')
    } finally {
      setLoading(false)
    }
  }

  const getSetting = (category: keyof AllSettings, key: string, defaultValue: any = null) => {
    return settings[category]?.[key as keyof any] ?? defaultValue
  }

  const getSystemName = () => getSetting('system', 'systemName', 'YouthConnect')
  const getTimezone = () => getSetting('system', 'timezone', 'UTC-5 (EST)')
  const getDateFormat = () => getSetting('system', 'dateFormat', 'MM/DD/YYYY')
  const isMaintenanceMode = () => getSetting('system', 'maintenanceMode', false)
  const isDebugMode = () => getSetting('system', 'debugMode', false)

  const getDefaultRole = () => getSetting('userManagement', 'defaultRole', 'Viewer')
  const isSelfRegistrationEnabled = () => getSetting('userManagement', 'selfRegistration', false)
  const getPasswordRequirements = () => getSetting('userManagement', 'passwordRequirements', 'Medium')
  const getSessionTimeout = () => getSetting('userManagement', 'sessionTimeout', 24)

  const getTwoFactorSetting = () => getSetting('security', 'twoFactor', 'Optional')
  const getLoginAttemptsLimit = () => getSetting('security', 'loginAttempts', 5)
  const getLockoutDuration = () => getSetting('security', 'lockoutDuration', 30)

  const areNewRegistrationAlertsEnabled = () => getSetting('notifications', 'newRegistrationAlerts', true)
  const isDailySummaryEnabled = () => getSetting('notifications', 'dailySummary', true)
  const getEmailNotifications = () => getSetting('notifications', 'emailNotifications', '<EMAIL>')

  return {
    settings,
    loading,
    error,
    refetch: fetchSettings,
    getSetting,
    
    // System settings
    getSystemName,
    getTimezone,
    getDateFormat,
    isMaintenanceMode,
    isDebugMode,
    
    // User management settings
    getDefaultRole,
    isSelfRegistrationEnabled,
    getPasswordRequirements,
    getSessionTimeout,
    
    // Security settings
    getTwoFactorSetting,
    getLoginAttemptsLimit,
    getLockoutDuration,
    
    // Notification settings
    areNewRegistrationAlertsEnabled,
    isDailySummaryEnabled,
    getEmailNotifications
  }
}
